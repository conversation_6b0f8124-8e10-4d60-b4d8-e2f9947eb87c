import React, { lazy, Suspense } from 'react';
import { HashRouter, Routes, Route, Navigate, Link } from 'react-router-dom';
import { AuthProvider } from '@/context/AuthContext';
import { useAuth } from '@/hooks/useAuth';
import { signOutUser } from '@/services/api';
import { Spinner } from '@/components/common/Spinner';
import { ErrorBoundary, AuthErrorBoundary } from '@/components/common/ErrorBoundary';
import { logger } from '@/utils/logger';
import {
  LogoutIcon,
  LeafIcon,
  HomeIcon,
  CalendarIcon,
  BellIcon,
  BookOpenIcon,
  SparklesIcon,
  ArchiveBoxIcon,
  QuestionMarkCircleIcon,
  BeakerIcon
} from '@/components/common/icons';
import { NotificationBadge } from '@/components/features/Notifications/NotificationBadge';
import { usePreventiveNotifications } from '@/hooks/useNotifications';
import { useArchive } from '@/hooks/useArchive';

// Lazy load screen components
const LoginScreen = lazy(() => import('@/components/features/LoginScreen'));
const DashboardScreen = lazy(() => import('@/components/features/DashboardScreen'));
const PlantDetailScreen = lazy(() => import('@/components/features/PlantDetailScreen'));
const CalendarView = lazy(() => import('@/components/features/Calendar/CalendarView'));
const NotificationCenter = lazy(() => import('@/components/features/Notifications/NotificationCenter'));
const GlobalJournal = lazy(() => import('@/components/features/Journal/GlobalJournal'));
const GeminiSettings = lazy(() => import('@/components/features/Notifications/GeminiSettings'));
const ArchiveManager = lazy(() => import('@/components/features/Archive/ArchiveManager'));
const HelpCenter = lazy(() => import('@/components/features/Help/HelpCenter'));
const FertilizerGuideScreen = lazy(() => import('@/components/features/FertilizerGuide/FertilizerGuideScreen'));

const Header: React.FC = () => {
    const { user } = useAuth();
    
    return (
        <header className="p-4 bg-[#1c1a31]/50 backdrop-blur-lg sticky top-0 z-40">
            <div className="container mx-auto flex justify-between items-center">
                <Link to="/" className="flex items-center gap-2">
                    <LeafIcon className="w-8 h-8 text-[#d385f5]" />
                    <span className="text-2xl font-bold text-white">FloraSynth</span>
                </Link>
                {user && (
                    <div className="flex items-center gap-4">
                        <nav className="flex items-center gap-4">
                            <Link
                                to="/"
                                className="flex items-center gap-2 text-sm text-gray-300 hover:text-white transition-colors"
                            >
                                <HomeIcon className="w-4 h-4" />
                                Mes Plantes
                            </Link>
                            <Link
                                to="/fertilizer-guide"
                                className="flex items-center gap-2 text-sm text-gray-300 hover:text-white transition-colors"
                            >
                                <BeakerIcon className="w-4 h-4" />
                                Guide Engrais
                            </Link>
                            <Link
                                to="/calendar"
                                className="flex items-center gap-2 text-sm text-gray-300 hover:text-white transition-colors"
                            >
                                <CalendarIcon className="w-4 h-4" />
                                Calendrier
                            </Link>
                            <Link
                                to="/notifications"
                                className="flex items-center gap-2 text-sm text-gray-300 hover:text-white transition-colors"
                            >
                                <BellIcon className="w-4 h-4" />
                                Notifications
                            </Link>
                            <Link
                                to="/journal"
                                className="flex items-center gap-2 text-sm text-gray-300 hover:text-white transition-colors"
                            >
                                <BookOpenIcon className="w-4 h-4" />
                                Journal
                            </Link>
                            <Link
                                to="/gemini-settings"
                                className="flex items-center gap-2 text-sm text-gray-300 hover:text-white transition-colors"
                            >
                                <SparklesIcon className="w-4 h-4" />
                                IA Gemini
                            </Link>
                            <Link
                                to="/archives"
                                className="flex items-center gap-2 text-sm text-gray-300 hover:text-white transition-colors"
                            >
                                <ArchiveBoxIcon className="w-4 h-4" />
                                Archives
                            </Link>
                            <Link
                                to="/help"
                                className="flex items-center gap-2 text-sm text-gray-300 hover:text-white transition-colors"
                            >
                                <QuestionMarkCircleIcon className="w-4 h-4" />
                                Aide
                            </Link>
                        </nav>
                        <NotificationBadge />
                        <span className="text-sm text-gray-300 hidden sm:block">Bienvenue, {user.displayName || user.email}</span>
                        <button
                            onClick={() => {
                                logger.debug('Clic sur le bouton de déconnexion', {
                                    component: 'App',
                                    action: 'LogoutButtonClick'
                                });
                                signOutUser();
                            }}
                            className="p-2 rounded-full hover:bg-white/10 transition-colors"
                        >
                           <LogoutIcon className="w-6 h-6 text-gray-300" />
                        </button>
                    </div>
                )}
            </div>
        </header>
    );
}

const ProtectedLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const { user, loading } = useAuth();

    // Activer les notifications préventives pour les utilisateurs connectés
    usePreventiveNotifications();

    // Vérifier et déclencher l'archivage automatique si nécessaire
    const { checkAutoArchive } = useArchive();
    React.useEffect(() => {
        if (user) {
            checkAutoArchive();
        }
    }, [user]); // Seulement dépendant de user pour éviter les boucles infinies

    if (loading) {
        return <div className="flex justify-center items-center h-screen"><Spinner size="lg" /></div>;
    }

    if (!user) {
        return <Navigate to="/login" replace />;
    }

    return (
        <>
            <Header />
            <main>
                {children}
            </main>
        </>
    );
};


const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <AuthErrorBoundary>
        <AuthProvider>
          <HashRouter>
            <Suspense fallback={<div className="flex justify-center items-center h-screen"><Spinner size="lg" /></div>}>
              <Routes>
                <Route path="/login" element={<LoginScreen />} />
                <Route path="/" element={<ProtectedLayout><DashboardScreen /></ProtectedLayout>} />
                <Route path="/plant/:plantId" element={<ProtectedLayout><PlantDetailScreen /></ProtectedLayout>} />
                <Route path="/calendar" element={<ProtectedLayout><CalendarView /></ProtectedLayout>} />
                <Route path="/notifications" element={<ProtectedLayout><NotificationCenter /></ProtectedLayout>} />
                <Route path="/journal" element={<ProtectedLayout><GlobalJournal /></ProtectedLayout>} />
                <Route path="/gemini-settings" element={<ProtectedLayout><GeminiSettings /></ProtectedLayout>} />
                <Route path="/archives" element={<ProtectedLayout><ArchiveManager /></ProtectedLayout>} />
                <Route path="/fertilizer-guide" element={<ProtectedLayout><FertilizerGuideScreen /></ProtectedLayout>} />
                <Route path="/help" element={<ProtectedLayout><HelpCenter /></ProtectedLayout>} />
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </Suspense>
          </HashRouter>
        </AuthProvider>
      </AuthErrorBoundary>
    </ErrorBoundary>
  );
};

export default App;